import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'محمد الأشرافي - مصمم مواقع ويب',
  description: 'محفظة أعمال محمد الأشرافي - مصمم مواقع ويب محترف متخصص في تطوير المواقع الحديثة والتصميم الإبداعي',
  keywords: 'محمد الأشرافي, تصميم مواقع, تطوير ويب, HTML, CSS, JavaScript, TypeScript, مصمم ويب',
  authors: [{ name: 'محمد الأشرافي' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'محمد الأشرافي - مصمم مواقع ويب',
    description: 'محفظة أعمال محمد الأشرافي - مصمم مواقع ويب محترف',
    type: 'website',
    locale: 'ar_SA',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className="font-arabic antialiased">
        <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900">
          {children}
        </div>
      </body>
    </html>
  )
}
