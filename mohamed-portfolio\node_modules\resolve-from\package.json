{"name": "resolve-from", "version": "4.0.0", "description": "Resolve the path of a module like `require.resolve()` but from a given path", "license": "MIT", "repository": "sindresorhus/resolve-from", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["require", "resolve", "path", "module", "from", "like", "import"], "devDependencies": {"ava": "*", "xo": "*"}}