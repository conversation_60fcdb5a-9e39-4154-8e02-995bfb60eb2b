'use client'

import { AuthProvider } from '@/lib/auth'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Hero from '@/components/sections/Hero'

export default function HomePage() {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900">
        <Header />

        <main>
          <Hero />

          {/* About Section Placeholder */}
          <section id="about" className="py-20 bg-dark-800/50">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center">
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-8">نبذة عني</h2>
                <p className="text-gray-400 max-w-3xl mx-auto">
                  قسم نبذة عني سيتم إضافته في المرحلة التالية...
                </p>
              </div>
            </div>
          </section>

          {/* Portfolio Section Placeholder */}
          <section id="portfolio" className="py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center">
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-8">أعمالي</h2>
                <p className="text-gray-400 max-w-3xl mx-auto">
                  قسم أعمالي سيتم إضافته في المرحلة التالية...
                </p>
              </div>
            </div>
          </section>

          {/* Courses Section Placeholder */}
          <section id="courses" className="py-20 bg-dark-800/50">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center">
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-8">الدورات</h2>
                <p className="text-gray-400 max-w-3xl mx-auto">
                  قسم الدورات سيتم إضافته في المرحلة التالية...
                </p>
              </div>
            </div>
          </section>

          {/* Contact Section Placeholder */}
          <section id="contact" className="py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center">
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-8">تواصل معي</h2>
                <p className="text-gray-400 max-w-3xl mx-auto">
                  قسم التواصل سيتم إضافته في المرحلة التالية...
                </p>
              </div>
            </div>
          </section>
        </main>

        <Footer />
      </div>
    </AuthProvider>
  )
}
