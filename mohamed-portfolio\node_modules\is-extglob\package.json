{"name": "is-extglob", "description": "Returns true if a string has an extglob.", "version": "2.1.1", "homepage": "https://github.com/jonschlinkert/is-extglob", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/is-extglob", "bugs": {"url": "https://github.com/jonschlinkert/is-extglob/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["has-glob", "is-glob", "micromatch"]}, "reflinks": ["verb", "verb-generate-readme"], "lint": {"reflinks": true}}}